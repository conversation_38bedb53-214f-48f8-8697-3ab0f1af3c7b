<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.home.HomeFragment">

    <Button
        android:id="@+id/btn_send_ussd"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="إرسال USSD"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="32dp" />

    <TextView
        android:id="@+id/tv_ussd_response"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="رد USSD سيظهر هنا"
        android:textAlignment="center"
        android:textSize="18sp"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/btn_send_ussd"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="24dp" />

    <TextView
        android:id="@+id/tv_service_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="حالة الخدمة: غير مفعلة"
        android:textAlignment="center"
        android:textSize="14sp"
        android:textColor="#FF0000"
        android:padding="8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_ussd_response"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_refresh_status"
        android:layout_marginTop="16dp" />

    <Button
        android:id="@+id/btn_refresh_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="تحديث"
        android:textSize="12sp"
        app:layout_constraintTop_toBottomOf="@id/tv_ussd_response"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp" />

    <TextView
        android:id="@+id/text_home"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:textAlignment="center"
        android:textSize="20sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>