<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2020. BoostTag E.I.R.L. Romell D.Z.
  ~ All rights reserved
  ~ porfile.romellfudi.com
  -->

<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:accessibilityEventTypes
        ="typeWindowStateChanged"
    android:packageNames="com.android.phone"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:accessibilityFlags="flagDefault"
    android:canRetrieveWindowContent="true"
    android:description="@string/accessibility_service_description"
    android:notificationTimeout="0"/>
    <!--|typeViewTextChanged-->