package com.example.ussdapp.ui.home;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.example.ussdapp.databinding.FragmentHomeBinding;
import com.romellfudi.ussdlibrary.USSDController;
import com.romellfudi.ussdlibrary.USSDController.CallbackInvoke;
import com.romellfudi.ussdlibrary.USSDController.CallbackMessage;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

public class HomeFragment extends Fragment {

    private FragmentHomeBinding binding;
    private static final int REQUEST_CALL_PERMISSION = 1001;
    private BroadcastReceiver ussdReceiver;
    private String ussdCode = "*222#";
    private static final String ACCESSIBILITY_SERVICE_NAME = "com.example.ussdapp/.UssdAccessibilityService";
    private static final String TAG = "HomeFragment";

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        try {
            HomeViewModel homeViewModel =
                    new ViewModelProvider(this).get(HomeViewModel.class);

            binding = FragmentHomeBinding.inflate(inflater, container, false);
            View root = binding.getRoot();

            final TextView textView = binding.textHome;
            homeViewModel.getText().observe(getViewLifecycleOwner(), textView::setText);

        Button btnSendUSSD = binding.btnSendUssd;
        btnSendUSSD.setOnClickListener(v -> {
            // تحقق من صلاحية overlay
            if (!USSDController.getInstance(requireActivity()).verifyOverlay(requireActivity())) {
                Toast.makeText(requireContext(), "يجب تفعيل صلاحية النافذة العائمة (Overlay) للتطبيق", Toast.LENGTH_LONG).show();
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + requireActivity().getPackageName()));
                startActivity(intent);
                return;
            }
            // تحقق من صلاحية إمكانية الوصول
            if (!USSDController.getInstance(requireActivity()).verifyAccessibilityAccess(requireActivity())) {
                Toast.makeText(requireContext(), "يجب تفعيل صلاحية إمكانية الوصول (Accessibility) للتطبيق", Toast.LENGTH_LONG).show();
                Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
                startActivity(intent);
                return;
            }
            // إعداد الكلمات المفتاحية للردود
            Map<String, HashSet<String>> map = new HashMap<>();
            map.put("KEY_LOGIN", new HashSet<>(Arrays.asList("espere", "waiting", "loading", "esperando")));
            map.put("KEY_ERROR", new HashSet<>(Arrays.asList("problema", "problem", "error", "null")));

            // استدعاء USSD عبر VoIpUSSD
            USSDController.getInstance(requireActivity()).callUSSDInvoke(
                "*222#", // كود USSD
                map,
                new CallbackInvoke() {
                    @Override
                    public void responseInvoke(String message) {
                        // الرد الأولي (يمكنك تجاهله أو عرضه)
                        requireActivity().runOnUiThread(() -> {
                            binding.tvUssdResponse.setText("جاري التنفيذ...\n" + message);
                        });
                    }

                    @Override
                    public void over(String message) {
                        // الرد النهائي من USSD
                        requireActivity().runOnUiThread(() -> {
                            binding.tvUssdResponse.setText(message);
                        });
                    }
                }
            );
        });

        Button btnRefreshStatus = binding.btnRefreshStatus;
        btnRefreshStatus.setOnClickListener(v -> {
            try {
                updateServiceStatus();
                Toast.makeText(requireContext(), "تم تحديث حالة الخدمة", Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                Toast.makeText(requireContext(), "خطأ في تحديث الحالة", Toast.LENGTH_SHORT).show();
            }
        });

        ussdReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                try {
                    if ("com.example.ussdapp.USSD_RESPONSE".equals(intent.getAction())) {
                        String response = intent.getStringExtra("response");
                        if (binding != null && binding.tvUssdResponse != null) {
                            binding.tvUssdResponse.setText(response);
                            Toast.makeText(requireContext(), "تم التقاط رد USSD بنجاح!", Toast.LENGTH_SHORT).show();
                            Log.d(TAG, "USSD response received: " + response);
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error receiving USSD response: " + e.getMessage());
                }
            }
        };
        } catch (Exception e) {
            // تجاهل الأخطاء في إنشاء العرض
        }
        
        // تحديث حالة الخدمة عند فتح التطبيق
        try {
            // تأخير قصير لضمان قراءة الإعدادات بشكل صحيح
            binding.getRoot().post(() -> {
                try {
                    updateServiceStatus();
                } catch (Exception e) {
                    // تجاهل الأخطاء
                }
            });
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
        
        return binding != null ? binding.getRoot() : null;
    }

    private void sendUSSDCode(String ussdCode) {
        try {
            if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{Manifest.permission.CALL_PHONE}, REQUEST_CALL_PERMISSION);
            } else {
                String encodedHash = Uri.encode("#");
                String ussd = ussdCode.replace("#", encodedHash);
                Intent intent = new Intent(Intent.ACTION_CALL, Uri.parse("tel:" + ussd));
                startActivity(intent);
                Toast.makeText(requireContext(), "جاري إرسال USSD: " + ussdCode, Toast.LENGTH_SHORT).show();
                Log.d(TAG, "USSD code sent: " + ussdCode);
            }
        } catch (Exception e) {
            Toast.makeText(requireContext(), "خطأ في إرسال USSD", Toast.LENGTH_SHORT).show();
            Log.e(TAG, "Error sending USSD: " + e.getMessage());
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        try {
            if (requestCode == REQUEST_CALL_PERMISSION && grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                sendUSSDCode(ussdCode);
            }
        } catch (Exception e) {
            // تجاهل الأخطاء في معالجة نتائج الأذونات
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        try {
            requireContext().registerReceiver(ussdReceiver, new IntentFilter("com.example.ussdapp.USSD_RESPONSE"), Context.RECEIVER_NOT_EXPORTED);
            updateServiceStatus();
        } catch (Exception e) {
            // تجاهل الأخطاء في تسجيل المستقبل
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        try {
            requireContext().unregisterReceiver(ussdReceiver);
        } catch (Exception e) {
            // تجاهل الأخطاء في إلغاء تسجيل المستقبل
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        try {
            binding = null;
        } catch (Exception e) {
            // تجاهل الأخطاء في تدمير العرض
        }
    }

    private boolean isAccessibilityServiceEnabled() {
        try {
            Log.d(TAG, "Checking accessibility service status...");
            
            // الطريقة الأولى: فحص إعدادات الوصول
            int accessibilityEnabled = 0;
            try {
                accessibilityEnabled = Settings.Secure.getInt(requireContext().getContentResolver(),
                        Settings.Secure.ACCESSIBILITY_ENABLED);
                Log.d(TAG, "Accessibility enabled: " + accessibilityEnabled);
            } catch (Settings.SettingNotFoundException e) {
                Log.e(TAG, "Error getting accessibility enabled: " + e.getMessage());
            }
            
            if (accessibilityEnabled == 1) {
                String settingValue = Settings.Secure.getString(requireContext().getContentResolver(),
                        Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
                Log.d(TAG, "Enabled services: " + settingValue);
                
                if (settingValue != null) {
                    // فحص دقيق لخدمتنا فقط
                    String[] ourServiceNames = {
                        "com.example.ussdapp/.UssdAccessibilityService",
                        "com.example.ussdapp/UssdAccessibilityService",
                        "com.example.ussdapp.UssdAccessibilityService"
                    };
                    
                    for (String serviceName : ourServiceNames) {
                        if (settingValue.contains(serviceName)) {
                            Log.d(TAG, "Our service found: " + serviceName);
                            return true;
                        }
                    }
                    
                    // فحص إضافي: البحث عن اسم التطبيق بالضبط
                    if (settingValue.contains("com.example.ussdapp")) {
                        Log.d(TAG, "Our app found in services");
                        return true;
                    }
                    
                    // إذا لم نجد خدمتنا، نتحقق من وجود خدمات أخرى
                    if (settingValue.contains("ussd") || settingValue.contains("Ussd")) {
                        Log.d(TAG, "Other USSD service found, but not ours: " + settingValue);
                        return false; // نعيد false لأنها ليست خدمتنا
                    }
                }
            }
            
            // الطريقة الثانية: فحص من خلال AccessibilityManager
            try {
                android.view.accessibility.AccessibilityManager am = 
                    (android.view.accessibility.AccessibilityManager) requireContext().getSystemService(Context.ACCESSIBILITY_SERVICE);
                if (am != null) {
                    List<android.accessibilityservice.AccessibilityServiceInfo> enabledServices = 
                        am.getEnabledAccessibilityServiceList(android.accessibilityservice.AccessibilityServiceInfo.FEEDBACK_ALL_MASK);
                    for (android.accessibilityservice.AccessibilityServiceInfo service : enabledServices) {
                        if (service.getResolveInfo() != null && 
                            service.getResolveInfo().serviceInfo != null &&
                            service.getResolveInfo().serviceInfo.packageName.equals("com.example.ussdapp")) {
                            Log.d(TAG, "Our service found by AccessibilityManager");
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in AccessibilityManager check: " + e.getMessage());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error in isAccessibilityServiceEnabled: " + e.getMessage());
        }
        
        Log.d(TAG, "Our service not found");
        return false;
    }

    private boolean isAccessibilityServiceEnabledAlternative() {
        try {
            // طريقة بديلة أبسط
            String enabledServices = Settings.Secure.getString(requireContext().getContentResolver(),
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
            
            if (enabledServices != null) {
                // فحص دقيق لخدمتنا فقط
                String[] ourServiceNames = {
                    "com.example.ussdapp/.UssdAccessibilityService",
                    "com.example.ussdapp/UssdAccessibilityService",
                    "com.example.ussdapp.UssdAccessibilityService",
                    "com.example.ussdapp"
                };
                
                String enabledServicesLower = enabledServices.toLowerCase();
                for (String serviceName : ourServiceNames) {
                    if (enabledServicesLower.contains(serviceName.toLowerCase())) {
                        Log.d(TAG, "Our service found by alternative method: " + serviceName);
                        return true;
                    }
                }
                
                // إذا لم نجد خدمتنا، نتحقق من وجود خدمات أخرى
                if (enabledServicesLower.contains("ussd") || enabledServicesLower.contains("usd")) {
                    Log.d(TAG, "Other USSD service found by alternative method, but not ours: " + enabledServices);
                    return false; // نعيد false لأنها ليست خدمتنا
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in alternative check: " + e.getMessage());
        }
        return false;
    }

    private boolean isAccessibilityServiceEnabledPackageManager() {
        try {
            // فحص من خلال PackageManager
            android.content.pm.PackageManager pm = requireContext().getPackageManager();
            android.content.pm.ServiceInfo[] services = pm.getPackageInfo("com.example.ussdapp", 
                    android.content.pm.PackageManager.GET_SERVICES).services;
            
            if (services != null) {
                for (android.content.pm.ServiceInfo service : services) {
                    if (service.name.contains("UssdAccessibilityService")) {
                        // الخدمة موجودة، الآن نتحقق من تفعيلها
                        String enabledServices = Settings.Secure.getString(requireContext().getContentResolver(),
                                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
                        if (enabledServices != null) {
                            // فحص دقيق لخدمتنا فقط
                            String[] ourServiceNames = {
                                service.name,
                                "com.example.ussdapp/.UssdAccessibilityService",
                                "com.example.ussdapp/UssdAccessibilityService",
                                "com.example.ussdapp"
                            };
                            
                            String enabledServicesLower = enabledServices.toLowerCase();
                            for (String serviceName : ourServiceNames) {
                                if (enabledServicesLower.contains(serviceName.toLowerCase())) {
                                    Log.d(TAG, "Our service found by PackageManager: " + serviceName);
                                    return true;
                                }
                            }
                            
                            // إذا لم نجد خدمتنا، نتحقق من وجود خدمات أخرى
                            if (enabledServicesLower.contains("ussd") || enabledServicesLower.contains("usd")) {
                                Log.d(TAG, "Other USSD service found by PackageManager, but not ours: " + enabledServices);
                                return false; // نعيد false لأنها ليست خدمتنا
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in PackageManager check: " + e.getMessage());
        }
        return false;
    }

    private void showAccessibilityServiceDialog() {
        try {
            Log.d(TAG, "Showing accessibility service dialog");
            
            // الحصول على معلومات تشخيصية
            String diagnosticInfo = "";
            try {
                String enabledServices = Settings.Secure.getString(requireContext().getContentResolver(),
                        Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
                if (enabledServices != null && !enabledServices.isEmpty()) {
                    // فحص إذا كانت هناك خدمات USSD أخرى مفعلة
                    if (enabledServices.contains("ussd") || enabledServices.contains("Ussd")) {
                        diagnosticInfo = "\n\nهناك خدمة USSD أخرى مفعلة:\n" + enabledServices + "\n\nيجب إلغاء تفعيل الخدمة الأخرى وتفعيل خدمتنا";
                        Log.d(TAG, "Dialog - Other USSD service is enabled: " + enabledServices);
                    } else {
                        diagnosticInfo = "\n\nالخدمات المفعلة حالياً:\n" + enabledServices;
                        Log.d(TAG, "Dialog - Enabled services: " + enabledServices);
                    }
                } else {
                    diagnosticInfo = "\n\nلا توجد خدمات مفعلة حالياً";
                    Log.d(TAG, "Dialog - No enabled services");
                }
                
                // فحص إضافي: التحقق من تفعيل الوصول بشكل عام
                try {
                    int accessibilityEnabled = Settings.Secure.getInt(requireContext().getContentResolver(),
                            Settings.Secure.ACCESSIBILITY_ENABLED);
                    if (accessibilityEnabled == 0) {
                        diagnosticInfo += "\n\nالوصول معطل بشكل عام";
                        Log.d(TAG, "Dialog - Accessibility is disabled globally");
                    }
                } catch (Settings.SettingNotFoundException e) {
                    diagnosticInfo += "\n\nلا يمكن قراءة إعدادات الوصول";
                    Log.e(TAG, "Dialog - Cannot read accessibility settings: " + e.getMessage());
                }
            } catch (Exception e) {
                diagnosticInfo = "\n\nلا يمكن قراءة قائمة الخدمات";
                Log.e(TAG, "Dialog - Error reading services: " + e.getMessage());
            }
            
            new androidx.appcompat.app.AlertDialog.Builder(requireContext())
                    .setTitle("تفعيل خدمة الوصول")
                    .setMessage("يجب تفعيل خدمة الوصول لالتقاط رد USSD. هل تريد فتح إعدادات الوصول؟" + diagnosticInfo)
                    .setPositiveButton("نعم", (dialog, which) -> {
                        try {
                            Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
                            startActivity(intent);
                            Log.d(TAG, "Opening accessibility settings");
                        } catch (Exception e) {
                            Toast.makeText(requireContext(), "لا يمكن فتح إعدادات الوصول", Toast.LENGTH_SHORT).show();
                            Log.e(TAG, "Error opening accessibility settings: " + e.getMessage());
                        }
                    })
                    .setNegativeButton("إلغاء", null)
                    .show();
        } catch (Exception e) {
            Toast.makeText(requireContext(), "يجب تفعيل خدمة الوصول من الإعدادات", Toast.LENGTH_LONG).show();
            Log.e(TAG, "Error showing dialog: " + e.getMessage());
        }
    }

    private void updateServiceStatus() {
        try {
            if (binding != null && binding.tvServiceStatus != null) {
                boolean isEnabled = isAccessibilityServiceEnabled();
                boolean isEnabledAlt = isAccessibilityServiceEnabledAlternative();
                boolean isEnabledPkg = isAccessibilityServiceEnabledPackageManager();
                
                Log.d(TAG, "Service status - Main: " + isEnabled + ", Alt: " + isEnabledAlt + ", Pkg: " + isEnabledPkg);
                
                if (isEnabled || isEnabledAlt || isEnabledPkg) {
                    binding.tvServiceStatus.setText("حالة الخدمة: مفعلة ✓");
                    binding.tvServiceStatus.setTextColor(requireContext().getResources().getColor(android.R.color.holo_green_dark));
                } else {
                    binding.tvServiceStatus.setText("حالة الخدمة: غير مفعلة ✗");
                    binding.tvServiceStatus.setTextColor(requireContext().getResources().getColor(android.R.color.holo_red_dark));
                    
                    // إضافة معلومات تشخيصية
                    try {
                        String enabledServices = Settings.Secure.getString(requireContext().getContentResolver(),
                                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
                        if (enabledServices != null && !enabledServices.isEmpty()) {
                            // فحص إذا كانت هناك خدمات USSD أخرى مفعلة
                            if (enabledServices.contains("ussd") || enabledServices.contains("Ussd")) {
                                binding.tvServiceStatus.setText("حالة الخدمة: غير مفعلة ✗\nهناك خدمة USSD أخرى مفعلة:\n" + enabledServices + "\n\nيجب إلغاء تفعيل الخدمة الأخرى وتفعيل خدمتنا");
                                Log.d(TAG, "Other USSD service is enabled: " + enabledServices);
                            } else {
                                binding.tvServiceStatus.setText("حالة الخدمة: غير مفعلة ✗\nالخدمات المفعلة: " + enabledServices);
                                Log.d(TAG, "Enabled services: " + enabledServices);
                            }
                        } else {
                            binding.tvServiceStatus.setText("حالة الخدمة: غير مفعلة ✗\nلا توجد خدمات مفعلة");
                            Log.d(TAG, "No enabled services found");
                        }
                        
                        // فحص إضافي: التحقق من تفعيل الوصول بشكل عام
                        try {
                            int accessibilityEnabled = Settings.Secure.getInt(requireContext().getContentResolver(),
                                    Settings.Secure.ACCESSIBILITY_ENABLED);
                            if (accessibilityEnabled == 0) {
                                binding.tvServiceStatus.setText("حالة الخدمة: غير مفعلة ✗\nالوصول معطل بشكل عام");
                                Log.d(TAG, "Accessibility is disabled globally");
                            }
                        } catch (Settings.SettingNotFoundException e) {
                            binding.tvServiceStatus.setText("حالة الخدمة: غير مفعلة ✗\nلا يمكن قراءة إعدادات الوصول");
                            Log.e(TAG, "Cannot read accessibility settings: " + e.getMessage());
                        }
                    } catch (Exception e) {
                        binding.tvServiceStatus.setText("حالة الخدمة: غير مفعلة ✗\nخطأ في قراءة الإعدادات");
                        Log.e(TAG, "Error reading settings: " + e.getMessage());
                    }
                }
                
                // إضافة معلومات تشخيصية إضافية
                try {
                    String enabledServices = Settings.Secure.getString(requireContext().getContentResolver(),
                            Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
                    if (enabledServices != null && !enabledServices.isEmpty()) {
                        // إضافة معلومات تشخيصية في حالة عدم اكتشاف الخدمة
                        if (!isEnabled && !isEnabledAlt && !isEnabledPkg) {
                            binding.tvServiceStatus.setText(binding.tvServiceStatus.getText() + "\n\nالخدمات المفعلة:\n" + enabledServices);
                            Log.d(TAG, "Diagnostic info - Enabled services: " + enabledServices);
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error getting diagnostic info: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in updateServiceStatus: " + e.getMessage());
        }
    }
}