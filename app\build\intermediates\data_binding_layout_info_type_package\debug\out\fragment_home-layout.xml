<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.example.ussdapp" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="69" endOffset="51"/></Target><Target id="@+id/btn_send_ussd" view="Button"><Expressions/><location startLine="8" startOffset="4" endLine="16" endOffset="41"/></Target><Target id="@+id/tv_ussd_response" view="TextView"><Expressions/><location startLine="18" startOffset="4" endLine="29" endOffset="41"/></Target><Target id="@+id/tv_service_status" view="TextView"><Expressions/><location startLine="31" startOffset="4" endLine="43" endOffset="41"/></Target><Target id="@+id/btn_refresh_status" view="Button"><Expressions/><location startLine="45" startOffset="4" endLine="54" endOffset="41"/></Target><Target id="@+id/text_home" view="TextView"><Expressions/><location startLine="56" startOffset="4" endLine="68" endOffset="51"/></Target></Targets></Layout>